<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="求职期望">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="form-list-item mainText font-w-500">求职类型</view>
        <view class="flex-1 flex-c flex-c-just">
          <view class="flex-1 text-r">
            <wd-radio-group v-model="jobType" shape="dot" inline checked-color="#FF8686">
              <wd-radio
                :value="Number(item.value)"
                v-for="(item, index) in radiolist1"
                :key="index"
              >
                {{ item.label }}
              </wd-radio>
            </wd-radio-group>
          </view>
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
      <view class="form-list flex-between" @click="hanRegion">
        <view class="form-list-item mainText font-w-500">工作地点</view>
        <view class="flex-1 flex-c flex-c-just">
          <view class="page_input1-text" :class="provinceName ? 'selelctColor' : 'nomalColor'">
            {{ provinceName ? provinceName + cityNameShow + districtName : '请选择期望地区' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between" @click="handCareer">
        <view class="form-list-item mainText font-w-500">期望岗位</view>
        <view class="flex-1 flex-c flex-c-just">
          <view
            class="page_input1-text"
            :class="positionObj.expectedPositions ? 'selelctColor' : 'nomalColor'"
          >
            {{ positionObj.expectedPositions ? positionObj.expectedPositions : '请选择期望岗位' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>

      <view class="form-list flex-between">
        <view class="form-list-item mainText font-w-500">期望薪资</view>
        <view class="flex-1 flex-c flex-c-just">
          <wd-picker
            ref="pickerPop"
            :columns="salaryColumns"
            label=""
            v-model="salaryValue"
            :column-change="onSalaryColumnChange"
            :display-format="salaryDisplayFormat"
            @confirm="handleSalaryConfirm"
            custom-class="custom-class"
          />

          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between" @click="handPosition">
        <view class="form-list-item mainText font-w-500">期望行业</view>
        <view class="flex-1 flex-c flex-c-just">
          <view class="page_input1-text" :class="industry ? 'selelctColor' : 'nomalColor'">
            {{ industry ? industry : '请选择期望行业' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          class="btn-delet m-r-30rpx"
          @click="delSubmit"
          :class="isAdd === 'edit' ? 'w-30' : ''"
        >
          删除
        </view>
        <view class="btn_box" :class="isAdd === 'edit' ? 'w-70' : 'w-100'">
          <view class="btn_bg" @click="submit">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>
<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import {
  userJobIntentionAdd,
  userJobIntentionUpdate,
  userJobIntentionDel,
} from '@/interPost/resume'
import { DICT_IDS } from '@/enum'
import { useLoginStore, useResumeStore } from '@/store'
import { useMessage } from 'wot-design-uni'
const { getDictData } = useDictionary()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const resumeStore = useResumeStore()
// vuex数据
const loginStore = useLoginStore()
const message = useMessage()
const salaryValue = ref(['面议', '面议'])
const isAdd = ref('')
// 工作编辑
const objItem = ref<AnyObject>({})
// 显示城市的字段
const cityNameShow = ref('')
// 数据响应式
const jobType = ref(1) // 工作类型
const id = ref(null) // 简历id
const cityObj = ref<AnyObject>({}) // 城市
const positionObj = ref<AnyObject>({
  expectedPositions: '',
  expectedPositionsCode: '',
}) // 岗位
const salaryExpectationStart = ref<any>(0) // 薪资开始
const salaryExpectationEnd = ref<any>(0) // 薪资结束
const provinceName = ref('') // 省份
const provinceCode = ref('')
const cityName = ref('') // 市
const cityCode = ref('')
const districtName = ref('') // 区
const districtCode = ref('')
const industry = ref('') // 行业
const industryId = ref(null)
const jobName = ref('')
const pickerPop = ref()
const seekObjEdit = ref<AnyObject>({})
// 基本数据
const radiolist1 = ref([])
// 返回
const back = () => {
  // console.log(provinceName.value, objItem.value.provinceName, 'provinceName.value')
  // console.log(cityName.value, objItem.value.cityName, 'cityName.value')
  // console.log(districtName.value, objItem.value.districtName, 'districtName.value')
  // console.log(
  //   salaryExpectationStart.value,
  //   objItem.value.salaryExpectationStart,
  //   'salaryExpectationStart.value',
  // )
  // console.log(
  //   salaryExpectationEnd.value,
  //   objItem.value.salaryExpectationEnd,
  //   'salaryExpectationEnd.value',
  // )
  // console.log(jobType.value, objItem.value.jobType, 'jobType.value')
  // console.log(industry.value, objItem.value.expectedIndustry, 'industry.value')
  // console.log(industryId.value, objItem.value.expectedIndustryCode, 'industryId.value')
  // console.log(
  //   positionObj.value.expectedPositions,
  //   objItem.value.expectedPositions,
  //   'positionObj.value.expectedPositions',
  // )
  if (isAdd.value === 'add') {
    // console.log(provinceName.value, 'provinceName.value=====')
    // console.log(cityName.value, 'cityName.value')
    // console.log(districtName.value, 'districtName.value')
    // console.log(salaryExpectationStart.value, 'salaryExpectationStart.value')
    // console.log(salaryExpectationEnd.value, 'salaryExpectationEnd.value')
    // console.log(jobType.value, 'jobType.value')
    // console.log(industry.value, 'industry.value')
    // console.log(positionObj.value.expectedPositions, 'positionObj.value.expectedPositions')
    if (
      provinceName.value === '' &&
      cityName.value === '' &&
      districtName.value === '' &&
      salaryExpectationStart.value === 0 &&
      salaryExpectationEnd.value === 0 &&
      jobType.value === 1 &&
      industry.value === '不限' &&
      positionObj.value.expectedPositionsCode === ''
    ) {
      loginStore.setCity({})
      loginStore.setpositionData({})
      loginStore.setindustryObj({})
      uni.navigateBack()
    } else {
      message
        .confirm({
          title: '提示',
          msg: '您有内容未提交保存,确认返回吗?',
        })
        .then(() => {
          loginStore.setCity({})
          loginStore.setpositionData({})
          loginStore.setindustryObj({})
          uni.navigateBack()
        })
    }
  } else if (
    provinceName.value === objItem.value.provinceName &&
    cityName.value === objItem.value.cityName &&
    districtName.value === objItem.value.districtName &&
    salaryExpectationStart.value === objItem.value.salaryExpectationStart &&
    salaryExpectationEnd.value === objItem.value.salaryExpectationEnd &&
    jobType.value === objItem.value.jobType &&
    industry.value === objItem.value.expectedIndustry &&
    positionObj.value.expectedPositions === objItem.value.expectedPositions
  ) {
    loginStore.setCity({})
    loginStore.setpositionData({})
    loginStore.setindustryObj({})
    uni.navigateBack()
  } else {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        loginStore.setCity({})
        loginStore.setpositionData({})
        loginStore.setindustryObj({})
        uni.navigateBack()
      })
  }
}

// 修改薪资数据
const salaryData = {
  面议: ['面议'], // 面议单独处理
  '1k': ['2k', '3k', '4k', '5k', '6k'],
  '2k': ['3k', '4k', '5k', '6k', '7k'],
  '3k': ['4k', '5k', '6k', '7k', '8k'],
  '4k': ['5k', '6k', '7k', '8k', '9k'],
  '5k': ['6k', '7k', '8k', '9k', '10k'],
  '6k': ['7k', '8k', '9k', '10k', '11k'],
  '7k': ['8k', '9k', '10k', '11k', '12k'],
  '8k': ['9k', '10k', '11k', '12k', '13k'],
  '9k': ['10k', '11k', '12k', '13k', '14k'],
  '10k': ['11k', '12k', '13k', '14k', '15k'],
  '11k': ['12k', '13k', '14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k'],
  '12k': ['13k', '14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k'],
  '13k': ['14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k'],
  '14k': ['15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k'],
  '15k': ['16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k'],
  '16k': ['17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k'],
  '17k': ['18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k'],
  '18k': ['19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k'],
  '19k': ['20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k'],
  '20k': ['21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k'],
  '21k': ['22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k'],
  '22k': ['23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k'],
  '23k': ['24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k'],
  '24k': ['25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k'],
  '25k': ['26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k'],
  '26k': ['27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k'],
  '27k': ['28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k'],
  '28k': ['29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k'],
  '29k': ['30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k'],
  '30k': ['31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k'],
  '31k': ['32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k'],
  '32k': ['33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k'],
  '33k': ['34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k'],
  '34k': ['35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k'],
  '35k': ['36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k'],
  '36k': ['37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k'],
  '37k': ['38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k'],
  '38k': ['39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k'],
  '39k': ['40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k'],
  '40k': ['41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k'],
  '41k': ['42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k'],
  '42k': ['43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k'],
  '43k': ['44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k'],
  '44k': ['45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k'],
  '45k': ['46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k'],
  '46k': ['47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k'],
  '47k': ['48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k'],
  '48k': ['49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k'],
  '49k': ['50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k', '59k'],
  '50k': ['51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k', '59k', '60k'],
}
// 删除
const delSubmit = async () => {
  message
    .confirm({
      title: '提示',
      content: '您确定要删除该条信息吗?',
    })
    .then(() => {
      userJobIntentionDel({ id: objItem.value.id }).then((res: any) => {
        if (res.code === 0) {
          loginStore.setCity({})
          loginStore.setpositionData({})
          loginStore.setindustryObj({})
          resumeStore.setIsRefresh(1)
          uni.navigateBack()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      })
    })
}

// 期望地区
const hanRegion = () => {
  uni.navigateTo({
    url: '/loginSetting/category/region',
  })
}
// 期待岗位
const handCareer = () => {
  uni.navigateTo({
    url: '/loginSetting/category/career',
  })
}
// 期待行业
const handPosition = () => {
  uni.navigateTo({
    url: '/loginSetting/category/expPosition',
  })
}
// 获取期待岗位
const submit = async () => {
  if (!provinceName.value) {
    uni.showToast({
      title: '请选择期望地区',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  if (!positionObj.value.expectedPositions) {
    uni.showToast({
      title: '请选择期望职业',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  if (isAdd.value === 'add') {
    const res: any = await userJobIntentionAdd({
      // 省
      provinceName: cityObj.value.provinceName,
      provinceCode: cityObj.value.provinceCode,
      //  城市编
      cityCode: cityObj.value.cityCode,
      cityName: cityObj.value.cityName,
      // 区域
      districtCode: cityObj.value.districtCode,
      districtName: cityObj.value.districtName,
      salaryExpectationStart: salaryExpectationStart.value,
      salaryExpectationEnd: salaryExpectationEnd.value,
      jobType: jobType.value,
      baseInfoId: id.value,
      industry: industry.value,
      industryId: industryId.value,
      expectedPositions: positionObj.value.expectedPositions,
      expectedPositionsCode: positionObj.value.expectedPositionsCode,
    })
    if (res.code === 0) {
      loginStore.setCity({})
      loginStore.setpositionData({})
      loginStore.setindustryObj({})
      resumeStore.setIsRefresh(1)
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    const res: any = await userJobIntentionUpdate({
      // 省
      provinceName: provinceName.value,
      provinceCode: provinceCode.value,
      //  城市编
      cityCode: cityCode.value,
      cityName: cityName.value,
      // 区域
      districtCode: districtCode.value,
      districtName: districtName.value,
      salaryExpectationStart: salaryExpectationStart.value,
      salaryExpectationEnd: salaryExpectationEnd.value || 0,
      jobType: jobType.value,
      baseInfoId: id.value,
      id: objItem.value.id,
      // 行业
      industry: industry.value,
      industryId: industryId.value,
      // 岗位
      expectedPositions: positionObj.value.expectedPositions,
      expectedPositionsCode: positionObj.value.expectedPositionsCode,
    })
    if (res.code === 0) {
      loginStore.setCity({})
      loginStore.setpositionData({})
      loginStore.setindustryObj({})
      resumeStore.setIsRefresh(1)
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
onLoad(async (options) => {
  await nextTick()
  // 简历id
  id.value = options.id
  isAdd.value = options.isAdd
  console.log(isAdd.value, 'isAdd.value')
  const res: any = await getDictData(DICT_IDS.JOB_EXPECTATIONS_PROVINCE)
  const expressiondata = res || res.data
  radiolist1.value = Object.entries(expressiondata).map(([key, value]) => ({
    value: key,
    label: value,
  }))
  if (isAdd.value === 'edit') {
    // console.log(options.item, 'options.item======================')
    objItem.value = JSON.parse(decodeURIComponent(options.item))
    // console.log(objItem.value.salaryExpectationStart, 'objItem.value======================')
    if (
      objItem.value.salaryExpectationStart === '面议' ||
      objItem.value.salaryExpectationStart === ''
    ) {
      salaryValue.value = ['面议', '面议']
      salaryExpectationStart.value = 0
      objItem.value.salaryExpectationStart = 0
      // console.log(
      //   objItem.value.salaryExpectationStart,
      //   'objItem.value.salaryExpectationStart======================',
      // )
    } else {
      salaryExpectationStart.value = objItem.value.salaryExpectationStart.replace('k', '000')
      objItem.value.salaryExpectationStart = salaryExpectationStart.value
      // 设置薪资选择器的值
      const startValue = objItem.value.salaryExpectationStart
      const endValue = objItem.value.salaryExpectationEnd || objItem.value.salaryExpectationStart
      // 将数字转换为对应的字符串格式
      const startStr = startValue ? startValue / 1000 + 'k' : '面议'
      const endStr = endValue ? endValue / 1000 + 'k' : '面议'
      salaryValue.value = [startStr, endStr]
    }
    if (
      objItem.value.salaryExpectationEnd === '面议' ||
      objItem.value.salaryExpectationEnd === ''
    ) {
      salaryExpectationEnd.value = 0
      objItem.value.salaryExpectationEnd = 0
    } else {
      salaryExpectationEnd.value = objItem.value.salaryExpectationEnd.replace('k', '000')
      objItem.value.salaryExpectationEnd = salaryExpectationEnd.value
      // 更新薪资选择器的结束值
      if (salaryValue.value[1] !== '面议') {
        const endStr = salaryExpectationEnd.value ? salaryExpectationEnd.value / 1000 + 'k' : '面议'
        salaryValue.value[1] = endStr
      }
    }
    console.log(objItem.value, 'objItem.item=====fh=================')
    // salaryValue.value =
    //   objItem.value.salaryExpectationStart === '面议'
    //     ? [0, 0]
    //     : [objItem.value.salaryExpectationStart, objItem.value.salaryExpectationEnd]
    // salaryExpectationStart.value =
    //   objItem.value.salaryExpectationStart === '面议'
    //     ? 0
    //     : objItem.value.salaryExpectationStart.replace('k', '000')
    // salaryExpectationEnd.value =
    //   objItem.value.salaryExpectationEnd === '面议' || objItem.value.salaryExpectationEnd === ''
    //     ? 0
    //     : objItem.value.salaryExpectationEnd.replace('k', '000')

    // 编辑模式下初始化数据
    initEditModeData()
  }

  // 初始化薪资选择器列数据
  initSalaryColumns()
})
onShow(async () => {
  await nextTick()
  console.log(isAdd.value, 'isAdd.value==============')
  if (isAdd.value === 'add') {
    // 区域
    cityObj.value = loginStore.cityObj
    // 省
    provinceName.value = cityObj.value.provinceName
      ? cityObj.value.provinceName
      : provinceName.value
    // 省code expectedCity
    provinceCode.value = cityObj.value.provinceCode
      ? cityObj.value.provinceCode
      : provinceCode.value
    // 市
    cityName.value = cityObj.value.cityName ? cityObj.value.cityName : cityName.value
    // 市code
    cityCode.value = cityObj.value.cityCode ? cityObj.value.cityCode : cityCode.value

    if (provinceName.value === cityName.value) {
      cityNameShow.value = ''
    } else {
      cityNameShow.value = cityObj.value.cityName ? cityObj.value.cityName : cityName.value
    }
    // 区
    districtName.value = cityObj.value.districtName
      ? cityObj.value.districtName
      : districtName.value
    // 区code
    districtCode.value = cityObj.value.districtCode
      ? cityObj.value.districtCode
      : districtCode.value

    // 岗位cityName
    positionObj.value = loginStore.positionObj?.expectedPositions
      ? loginStore.positionObj
      : positionObj.value
    console.log(positionObj.value, 'positionObj.value======')
    // 新增模式下确保薪资默认为面议
    if (salaryValue.value[0] !== '面议') {
      salaryValue.value = ['面议', '面议']
    }
  } else {
    console.log(loginStore.cityObj, '编辑=====')

    // 编辑模式下，如果从其他页面返回，需要更新数据
    if (loginStore.cityObj && Object.keys(loginStore.cityObj).length > 0) {
      initEditModeData()
    }
    if (loginStore.positionObj && Object.keys(loginStore.positionObj).length > 0) {
      positionObj.value = loginStore.positionObj
      loginStore.setpositionData(positionObj.value)
    }
  }

  // 处理行业数据（新增和编辑模式都需要）
  // const jobList = loginStore.jobObj
  // if (jobList && jobList.length > 0) {
  //   industry.value =
  //     jobList
  //       .map((item) => item?.name)
  //       .filter(Boolean)
  //       .join(',') || '不限'
  //   industryId.value =
  //     jobList
  //       .map((item) => item?.code)
  //       .filter(Boolean)
  //       .join(',') || 0
  //   jobName.value = industry.value
  // }
  console.log(loginStore.industryObj, 'loginStore.industryObj=====')
  if (loginStore.industryObj?.industryName) {
    industry.value = loginStore.industryObj.industryName
    industryId.value = loginStore.industryObj.industryCode
  }
  nextTick(() => {
    initSalaryColumns()
  })
})
const salaryColumns = ref([
  Object.keys(salaryData).map((item) => ({ label: item, value: item })),
  salaryData['面议'].map((item) => ({ label: item, value: item })), // 初始化为面议对应的选项
])
onUnmounted(() => {
  loginStore.setCity({})
  loginStore.setpositionData({})
  loginStore.setindustryObj({})
})
// 编辑模式下初始化数据
const initEditModeData = () => {
  // 区域
  cityObj.value = loginStore.cityObj
  console.log(cityObj.value, 'cityObj.value======')
  console.log(objItem.value, 'objItem.value======')

  // 安全访问 loginStore.cityObj 的属性
  const storeCityObj = loginStore.cityObj as AnyObject

  // 省
  provinceName.value = storeCityObj?.provinceName
    ? storeCityObj.provinceName
    : objItem.value.provinceName
  // 省code expectedCity
  provinceCode.value = cityObj.value?.provinceCode
    ? cityObj.value.provinceCode
    : objItem.value.provinceCode
  // 市
  cityName.value = cityObj.value?.cityName ? cityObj.value.cityName : objItem.value.cityName
  // 市code
  cityCode.value = cityObj.value?.cityCode ? cityObj.value.cityCode : objItem.value.cityCode
  if (provinceName.value === cityName.value) {
    cityNameShow.value = ''
  } else {
    cityNameShow.value = cityObj.value.cityName ? cityObj.value.cityName : objItem.value.cityName
  }
  // 区
  if (cityObj.value.districtName) {
    districtName.value = cityObj.value.districtName
  } else if (!cityObj.value.districtName && cityObj.value.cityName) {
    districtName.value = ''
  } else {
    districtName.value = objItem.value.districtName
  }
  if (cityObj.value.districtCode) {
    districtCode.value = cityObj.value.districtCode
  } else if (!cityObj.value.districtCode && cityObj.value.cityCode) {
    districtCode.value = ''
  } else {
    districtCode.value = objItem.value.districtCode
  }

  // 显示图标
  const city = {
    //  省份编码
    provinceName: provinceName.value,
    provinceCode: provinceCode.value,
    //  城市编
    cityCode: cityCode.value,
    cityName: cityName.value,
    // 区域
    districtCode: districtCode.value,
    districtName: districtName.value,
  }
  console.log(city, 'city====')
  loginStore.setCity(city)
  // 岗位cityName
  const obj = {
    expectedPositions: objItem.value.expectedPositions,
    expectedPositionsCode: Number(objItem.value.expectedPositionsCode),
  }
  // console.log(obj, loginStore.positionObj, 'obj')
  positionObj.value = (loginStore.positionObj as AnyObject)?.expectedPositions
    ? loginStore.positionObj
    : obj
  // console.log(positionObj.value, 'positionObj.value===')
  loginStore.setpositionData(positionObj.value)

  // 设置工作类型
  jobType.value = objItem.value.jobType

  // 设置行业信息
  industry.value = objItem.value.expectedIndustry || '不限'
  industryId.value = objItem.value.industryId || 0
  jobName.value = industry.value

  // 初始化薪资选择器
  nextTick(() => {
    initSalaryColumns()
  })
}

// 初始化薪资选择器的第二列数据
const initSalaryColumns = () => {
  const firstValue = salaryValue.value[0]
  if (firstValue && salaryData[firstValue]) {
    salaryColumns.value[1] = salaryData[firstValue].map((item) => ({ label: item, value: item }))
  } else if (firstValue === '面议') {
    // 如果第一列是面议，第二列也设置为面议
    salaryColumns.value[1] = [{ label: '面议', value: '面议' }]
  }
}

// 修改列变化处理
const onSalaryColumnChange = (picker, values, columnIndex, resolve) => {
  if (columnIndex === 0) {
    const selected = values[0]?.value || '面议'
    if (selected === '面议') {
      picker.setColumnData(1, [{ label: '面议', value: '面议' }])
    } else {
      picker.setColumnData(
        1,
        salaryData[selected].map((item: any) => ({ label: item, value: item })),
      )
    }
    resolve()
  }
}

const salaryDisplayFormat = (items: any) => {
  // 处理空值或无效值的情况
  if (!items || items.length < 2) {
    return '面议'
  }

  // 获取标签值，处理可能的undefined情况
  const firstLabel = items[0]?.label || '面议'
  const secondLabel = items[1]?.label || '面议'

  // 如果任一选择的是面议，只显示一个"面议"
  if (firstLabel === '面议' || secondLabel === '面议') {
    return '面议'
  }

  // 如果两个值相同，只显示一个
  if (firstLabel === secondLabel) {
    return firstLabel
  }

  // 其他情况正常显示范围
  return `${firstLabel}-${secondLabel}`
}
const handleSalaryConfirm = ({ value }) => {
  // 处理面议情况
  if (value[0] === '面议' || value[1] === '面议') {
    salaryExpectationStart.value = 0
    salaryExpectationEnd.value = 0
  } else if (value[0].indexOf('k') !== -1 && value[1].indexOf('k') !== -1) {
    // 处理具体薪资范围
    salaryExpectationStart.value = value[0].replace('k', '000') // 薪资开始
    salaryExpectationEnd.value = value[1].replace('k', '000') // 薪资结束
  } else {
    // 其他情况设为0（面议）
    salaryExpectationStart.value = 0
    salaryExpectationEnd.value = 0
  }
}
</script>

<style lang="scss" scoped>
::v-deep .wd-radio-group {
  padding-right: 0rpx !important;
  background-color: transparent;
}
::v-deep .wd-picker__arrow,
.wd-picker__clear {
  display: none;
}
.selelctColor {
  color: #333333;
}

.nomalColor {
  color: #888888;
}
.page_input1-text {
  font-size: 32rpx;
}
::v-deep .wd-picker__cell {
  padding-right: 0rpx !important;
  background: transparent;
}
::v-deep .wd-picker__value {
  margin-right: 0rpx;
  font-size: 32rpx;
}
::v-deep .wd-radio__label {
  font-size: 32rpx;
  color: #333;
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }
  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
.pageContaner {
  padding: 20rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      width: 160rpx;
      // color: #0f0f0f;
    }

    .arrow-right-icon {
      display: flex;
      justify-content: right;
      width: 40rpx;
    }
  }
}
</style>
