<template>
  <wd-popup v-model="localShow" @close="handleClose">
    <z-paging ref="paging">
      <template #top>
        <CustomNavBar />
      </template>
      <view class="protocol-content">测试</view>
      <template #bottom>
        <view class="flex justify-center items-center p-t-40rpx">
          <view
            class="w-300rpx bg-#ff0000 text-center py-20rpx rounded-60rpx c-#fff m-r-40rpx"
            @click="handleClose"
          >
            拒绝
          </view>
          <view
            @click="handleAgree"
            class="w-300rpx bg-#4d8fff text-center py-20rpx rounded-60rpx c-#fff"
          >
            同意
          </view>
        </view>
      </template>
    </z-paging>
  </wd-popup>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:show'])
// 新增本地变量 localShow
const localShow = ref(props.show)
// 监听 props.show 变化同步 localShow
watch(
  () => props.show,
  (val) => {
    localShow.value = val
  },
)
// 监听 localShow 变化，通知父组件
watch(localShow, (val) => {
  emit('update:show', val)
})

const handleClose = () => {
  localShow.value = false
}
// 同意
const handleAgree = () => {}
</script>

<style lang="scss" scoped></style>
