import { ref, computed } from 'vue'

// 导入所有福利图标
import benefits1 from '@/resumeRelated/img/benefits/benefits_1.png'
import benefits2 from '@/resumeRelated/img/benefits/benefits_2.png'
import benefits3 from '@/resumeRelated/img/benefits/benefits_3.png'
import benefits4 from '@/resumeRelated/img/benefits/benefits_4.png'
import benefits5 from '@/resumeRelated/img/benefits/benefits_5.png'
import benefits6 from '@/resumeRelated/img/benefits/benefits_6.png'
import benefits7 from '@/resumeRelated/img/benefits/benefits_7.png'
import benefits8 from '@/resumeRelated/img/benefits/benefits_8.png'
import benefits9 from '@/resumeRelated/img/benefits/benefits_9.png'
import benefits10 from '@/resumeRelated/img/benefits/benefits_10.png'
import benefits11 from '@/resumeRelated/img/benefits/benefits_11.png'
import benefits12 from '@/resumeRelated/img/benefits/benefits_12.png'
import benefits13 from '@/resumeRelated/img/benefits/benefits_13.png'
import benefits14 from '@/resumeRelated/img/benefits/benefits_14.png'
import benefits15 from '@/resumeRelated/img/benefits/benefits_15.png'
import benefits16 from '@/resumeRelated/img/benefits/benefits_16.png'
import benefits17 from '@/resumeRelated/img/benefits/benefits_17.png'
import benefits18 from '@/resumeRelated/img/benefits/benefits_18.png'
import benefits19 from '@/resumeRelated/img/benefits/benefits_19.png'
import benefits20 from '@/resumeRelated/img/benefits/benefits_20.png'
import benefitsCustom from '@/resumeRelated/img/benefits/benefits_custom.png'

/**
 * 福利待遇图标映射hooks
 * 根据福利待遇名称匹配对应的图标
 */
export const useBenefitsIcon = () => {
  // 福利待遇与图标的映射关系
  const benefitsIconMap = ref<Record<string, string>>({
    年终奖: benefits1,
    定期团建: benefits2,
    零食下午茶: benefits3,
    节日福利: benefits4,
    生日福利: benefits5,
    五险一金: benefits6,
    带薪年假: benefits7,
    员工食堂: benefits8,
    定期体检: benefits9,
    交通补贴: benefits10,
    通讯补贴: benefits11,
    餐饮补贴: benefits12,
    住房补贴: benefits13,
    免费工作餐: benefits14,
    提供住宿: benefits15,
    免费班车: benefits16,
    岗位晋升: benefits17,
    年度调薪: benefits18,
    高温补贴: benefits19,
    弹性工作: benefits20,
  })

  /**
   * 根据福利待遇名称获取对应图标
   * @param welfareTreatment 福利待遇名称
   * @returns 对应的图标路径，如果没有匹配则返回默认图标
   */
  const getBenefitsIcon = (welfareTreatment: string): string => {
    return benefitsIconMap.value[welfareTreatment] || benefitsCustom
  }

  /**
   * 批量获取福利待遇图标列表
   * @param benefitsList 福利待遇列表
   * @returns 对应的图标路径数组
   */
  const getBenefitsIconList = (benefitsList: Array<{ welfareTreatment: string }>): string[] => {
    return benefitsList.map((item: any) => getBenefitsIcon(item.welfareTreatment))
  }

  /**
   * 检查是否有对应的图标
   * @param welfareTreatment 福利待遇名称
   * @returns 是否有对应的图标
   */
  const hasBenefitsIcon = (welfareTreatment: string): boolean => {
    return welfareTreatment in benefitsIconMap.value
  }

  /**
   * 添加或更新福利待遇图标映射
   * @param welfareTreatment 福利待遇名称
   * @param iconPath 图标路径
   */
  const setBenefitsIcon = (welfareTreatment: string, iconPath: string): void => {
    benefitsIconMap.value[welfareTreatment] = iconPath
  }

  /**
   * 获取所有已映射的福利待遇名称
   * @returns 福利待遇名称数组
   */
  const getAllMappedBenefits = computed(() => {
    return Object.keys(benefitsIconMap.value)
  })

  return {
    benefitsIconMap,
    getBenefitsIcon,
    getBenefitsIconList,
    hasBenefitsIcon,
    setBenefitsIcon,
    getAllMappedBenefits,
  }
}
