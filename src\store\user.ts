import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { USER_TYPE } from '@/enum'
import { currentValueOf, timeDiffValueOf, valueOfToDate } from '@/utils/days'

type UserType = Partial<Api.User.IUserInfo>
const initState: UserType = {
  token: '',
  type: USER_TYPE.APPLICANT,
  hrexamineStateObj: {
    auditStatus: null,
    status: null,
  },
}
export const useUserStore = defineStore(
  'user',
  () => {
    const { logoutEaseMobIM } = useEaseMobIM()
    const { stopKeepAlive } = useKeepAlive()
    const { closePushNotifications } = useIMConversation()
    const userInfo = ref<UserType>({ ...initState })
    const isLoginEd = computed(() => !!userInfo.value.token)
    const setUserLoginTime = () => {
      userInfo.value.loginTime = currentValueOf()
    }
    const setUserInfo = (val: UserType) => {
      const { token } = val
      userInfo.value = {
        ...userInfo.value,
        ...val,
      }
      // userInfo.value.type = 1 // 本地测用
      setUserToken(token)
    }
    const setUserRoleType = (type: Api.Common.USER_TYPE) => {
      userInfo.value.type = type
    }
    const setUserToken = (token?: string) => {
      userInfo.value.token = token
      setUserLoginTime()
    }
    const getUserLoginTimeDiff = () => {
      return timeDiffValueOf(valueOfToDate(userInfo.value.loginTime), 'days')
    }
    const getToken = () => {
      return userInfo.value?.token ?? ''
    }
    const getHrExamineStateObj = () => {
      return userInfo.value.hrexamineStateObj
    }
    const clearUserInfo = () => {
      logoutEaseMobIM()
      stopKeepAlive()
      closePushNotifications()
      userInfo.value = { ...initState }
    }

    const setHrExamineStateObj = (val: any) => {
      userInfo.value.hrexamineStateObj = val
    }
    // 新增：全局弹框弹出状态
    const hasShownAgreement = ref(false)
    const setHasShownAgreement = (val: boolean) => {
      hasShownAgreement.value = val
    }
    const getHasShownAgreement = () => hasShownAgreement.value
    return {
      userInfo,
      isLoginEd,
      setUserInfo,
      setUserRoleType,
      setUserToken,
      getToken,
      getUserLoginTimeDiff,
      clearUserInfo,
      setHrExamineStateObj,
      getHrExamineStateObj,
      hasShownAgreement,
      setHasShownAgreement,
      getHasShownAgreement,
    }
  },
  {
    persist: true,
  },
)
