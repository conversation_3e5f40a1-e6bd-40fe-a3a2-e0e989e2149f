<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" @query="queryList" v-model="pageData" ref="pagingRef">
    <template #top>
      <CustomNavBar title="地址"></CustomNavBar>
    </template>
    <view class="px-60rpx py-20rpx">
      <view
        class="px-40rpx py-40rpx bg-#fff rounded-[30rpx] m-b-25rpx shadow-[8rpx_8rpx_32rpx_0rpx_rgba(0,0,0,0.1)]"
        v-for="(item, index) in pageData"
        :key="index"
        @click="handleActiveAddress(item)"
      >
        <view class="relative">
          <view class="flex items-center">
            <wd-img :width="19" :height="19" :src="position" class="m-r-10rpx" />
            <view
              class="flex-1 text-28rpx c-#000"
              style="word-break: break-all; white-space: normal"
            >
              {{ item.provideName }}{{ item.cityName === item.provideName ? '' : item.cityName }}
              {{ item.districtName }}
              {{ item.address }}
              <text
                class="status-tag"
                :class="{
                  'bg-#BEBEBE': item.status === 0,
                  'bg-#00BF19': item.status === 1,
                  'bg-#FF4141': item.status === 2,
                  'bg-#FF1000': item.status === 3,
                }"
              >
                {{
                  item.status === 0
                    ? '审核中'
                    : item.status === 1
                      ? '正常'
                      : item.status === 2
                        ? '未通过'
                        : '下架'
                }}
              </text>
            </view>

            <view
              class="absolute right-[-10rpx] top-1/2 transform -translate-y-1/2"
              v-if="item.status === 2 || item.status === 3"
              @click="delet(item.id)"
            >
              <wd-img :width="20" :height="20" :src="del"></wd-img>
            </view>
          </view>
        </view>
      </view>
    </view>

    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box">
          <view class="btn_bg" @click="goAddressPage">添加新的地址</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import del from '@/resumeRelated/img/del.png'
import { queryListAdress, deleteAdress, queryPassList } from '@/service/companyAdress'
import { truncateText } from '@/utils/util'
import { useMessage } from 'wot-design-uni'
import position from '@/setting/img/position.png'

const message = useMessage()
const { pageParams } = usePagePeriod()
const { pageStyle, pageData, pageInfo, pagingRef, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { releaseActiveAddress } = useReleasePost()

const sourceIsRelease = computed(() => pageParams.value?.source === 'release')
const source = ref('')
// 展开状态管理
const expandedItems = ref<Record<number, boolean>>({})

// 切换展开/收起状态
const toggleExpand = (index: number) => {
  expandedItems.value[index] = !expandedItems.value[index]
}

// 添加地址
const goAddressPage = () => {
  uni.navigateTo({
    url: '/sub_business/pages/AddressCenter/AddressAdd',
  })
}
// 编辑
const edit = (item) => {
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/sub_business/pages/AddressCenter/AddressAdd?item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
function handleActiveAddress(item: AnyObject) {
  if (sourceIsRelease.value) {
    releaseActiveAddress.value = item
    uni.navigateBack()
  }
}
// s删除
const delet = async (id) => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除吗?',
    })
    .then(() => {
      deleteAdress({ id }).then((res: any) => {
        if (res.code === 0) {
          pagingRef.value.reload()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      })
    })
}
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res =
    source.value === 'release'
      ? await queryPassList({
          entity: {},
          orderBy: {},
          page: pageInfo.page,
          size: pageInfo.size,
        })
      : await queryListAdress({
          entity: {},
          orderBy: {},
          page: pageInfo.page,
          size: pageInfo.size,
        })

  console.log(res, 'res=============')
  if (res.code === 0) {
    pagingRef.value.complete(res.data.list)
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  source.value = options?.source || ''
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.address-content {
  overflow: hidden;
  text-overflow: ellipsis;

  // 默认显示一行，超出部分用省略号
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;

  &.address-expanded {
    word-break: break-all;
    // 展开时显示全部内容，允许换行
    white-space: normal;
  }
}

.address-line {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx; // 状态标签与地址间距
  align-items: center;
}

.status-tag {
  display: inline-block;
  flex-shrink: 0;
  padding: 2rpx 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #fff;
  text-align: center;
  border-radius: 5rpx;
}

.btn-fixed {
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 25rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
